#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的FIFA 21数据分析 - 15个问题
"""

import pandas as pd
import sys
from io import StringIO

def capture_output(func):
    """捕获函数的输出"""
    old_stdout = sys.stdout
    sys.stdout = captured_output = StringIO()
    try:
        func()
    finally:
        sys.stdout = old_stdout
    return captured_output.getvalue()

def question_01():
    """问题1：FIFA 21中潜力值超过90的球员中，所在俱乐部转会预算超过5000万欧元且有国家队的球员有哪些？"""
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    potential_players = players_df[(players_df['潜力值'] > 90) & (players_df['国家队'].notna()) & (players_df['国家队'] != 'Not in team')].copy()
    rich_clubs = teams_df[teams_df['转会预算'] > 50000000].copy()
    
    result = pd.merge(
        potential_players[['姓名', '俱乐部', '国家队', '潜力值']], 
        rich_clubs[['俱乐部', '转会预算']], 
        on='俱乐部', 
        how='inner'
    )
    
    result = result.sort_values('潜力值', ascending=False)
    result['转会预算(百万欧元)'] = (result['转会预算'] / 1000000).round(1)
    final_result = result[['姓名', '国家队', '俱乐部', '转会预算(百万欧元)']]
    
    print(f"符合条件的球员数量: {len(final_result)}")
    print(final_result.to_markdown(index=False))

def question_02():
    """问题2：FIFA 21中综合评分超过90且惯用脚为左脚的球员中，所在俱乐部国际声望超过5的球员占比是多少？"""
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    left_foot_players = players_df[(players_df['综合评分'] > 90) & (players_df['惯用脚'] == 'Left')].copy()
    high_reputation_clubs = teams_df[teams_df['国际声望'] > 5].copy()
    
    result = pd.merge(
        left_foot_players[['姓名', '俱乐部', '综合评分', '惯用脚']], 
        high_reputation_clubs[['俱乐部', '国际声望']], 
        on='俱乐部', 
        how='inner'
    )
    
    total_left_foot = len(left_foot_players)
    high_reputation_count = len(result)
    percentage = (high_reputation_count / total_left_foot * 100) if total_left_foot > 0 else 0
    
    result_summary = pd.DataFrame({
        '指标': ['综合评分>90且惯用脚为左脚的球员总数', '其中所在俱乐部国际声望>5的球员数', '占比(%)'],
        '数值': [total_left_foot, high_reputation_count, f'{percentage:.2f}%']
    })
    
    print(result_summary.to_markdown(index=False))

def question_03():
    """问题3：FIFA 21中综合评分超过85且周薪超过20万欧元的球员中，所在俱乐部国内声望超过5的球员有哪些？"""
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    high_salary_players = players_df[(players_df['综合评分'] > 85) & (players_df['周薪（欧元）'] > 200000)].copy()
    high_domestic_clubs = teams_df[teams_df['国内声望'] > 5].copy()
    
    result = pd.merge(
        high_salary_players[['姓名', '俱乐部', '综合评分', '周薪（欧元）']], 
        high_domestic_clubs[['俱乐部', '国内声望', '国际声望']], 
        on='俱乐部', 
        how='inner'
    )
    
    result = result.sort_values('周薪（欧元）', ascending=False)
    result['周薪(万欧元)'] = (result['周薪（欧元）'] / 10000).round(1)
    final_result = result[['姓名', '俱乐部', '周薪(万欧元)', '国内声望']]
    
    print(f"符合条件的球员数量: {len(final_result)}")
    print(final_result.to_markdown(index=False))

def question_04():
    """问题4：FIFA 21中综合评分超过90且合同到期时间为2023年的球员中，所在俱乐部转会预算最高的球员是谁？"""
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    contract_players = players_df[(players_df['综合评分'] > 90) & (players_df['合同到期时间'] == 2023)].copy()
    
    if len(contract_players) == 0:
        print("没有符合条件的球员")
        return
    
    result = pd.merge(
        contract_players[['姓名', '俱乐部', '综合评分', '合同到期时间']], 
        teams_df[['俱乐部', '转会预算']], 
        on='俱乐部', 
        how='inner'
    )
    
    if len(result) == 0:
        print("没有符合条件的球员")
        return
    
    # 找到转会预算最高的球员
    max_budget_player = result.loc[result['转会预算'].idxmax()]
    
    result_df = pd.DataFrame({
        '球员姓名': [max_budget_player['姓名']],
        '俱乐部': [max_budget_player['俱乐部']],
        '合同到期时间': [max_budget_player['合同到期时间']],
        '转会预算(百万欧元)': [max_budget_player['转会预算']/1000000]
    })
    
    print(result_df.to_markdown(index=False))

def question_05():
    """问题5：FIFA 21中综合评分超过85且惯用脚为右脚的球员中，所在俱乐部联赛为"Spanish Primera División (1)"的球员平均周薪是多少？"""
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    right_foot_players = players_df[(players_df['综合评分'] > 85) & (players_df['惯用脚'] == 'Right')].copy()
    spanish_clubs = teams_df[teams_df['联赛'] == 'Spanish Primera División (1)'].copy()
    
    result = pd.merge(
        right_foot_players[['姓名', '俱乐部', '综合评分', '惯用脚', '周薪（欧元）']], 
        spanish_clubs[['俱乐部', '联赛']], 
        on='俱乐部', 
        how='inner'
    )
    
    if len(result) > 0:
        avg_salary = result['周薪（欧元）'].mean()
        
        summary = pd.DataFrame({
            '指标': ['符合条件的球员数量', '平均周薪(万欧元)'],
            '数值': [len(result), f'{avg_salary/10000:.2f}']
        })
        
        print(summary.to_markdown(index=False))
    else:
        print("没有符合条件的球员")

# 继续添加其他问题...
def main():
    """运行所有问题并生成结果文档"""
    questions = [
        ("问题1：FIFA 21中潜力值超过90的球员中，所在俱乐部转会预算超过5000万欧元且有国家队的球员有哪些？", question_01),
        ("问题2：FIFA 21中综合评分超过90且惯用脚为左脚的球员中，所在俱乐部国际声望超过5的球员占比是多少？", question_02),
        ("问题3：FIFA 21中综合评分超过85且周薪超过20万欧元的球员中，所在俱乐部国内声望超过5的球员有哪些？", question_03),
        ("问题4：FIFA 21中综合评分超过90且合同到期时间为2023年的球员中，所在俱乐部转会预算最高的球员是谁？", question_04),
        ("问题5：FIFA 21中综合评分超过85且惯用脚为右脚的球员中，所在俱乐部联赛为西甲的球员平均周薪是多少？", question_05),
    ]
    
    results = []
    
    print("开始运行所有问题...")
    
    for i, (title, func) in enumerate(questions, 1):
        print(f"\n{'='*80}")
        print(f"{title}")
        print("="*80)
        output = capture_output(func)
        print(output)
        results.append((f"问题{i}", title, output))
    
    # 生成结果文档
    with open('results.md', 'w', encoding='utf-8') as f:
        f.write("# FIFA 21 数据分析结果\n\n")
        f.write("本文档包含了15个数据分析问题的结果。所有问题都需要关联球员表和球队表进行多表查询。\n\n")
        
        for question_num, title, output in results:
            f.write(f"## {question_num}\n\n")
            f.write(f"**问题描述：** {title}\n\n")
            f.write(f"**分析结果：**\n\n")
            f.write(f"```\n{output}\n```\n\n")
    
    print(f"\n{'='*80}")
    print("所有问题运行完成！结果已保存到 results.md 文件中")
    print("="*80)

if __name__ == "__main__":
    main()
