#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题2：FIFA 21中潜力值超过90且年龄小于25岁的球员中，所在俱乐部国际声望超过5的球员占比是多少？
"""

import pandas as pd

def main():
    # 读取数据文件
    print("正在读取数据文件...")
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    # 筛选潜力值超过90且年龄小于25岁的球员
    young_potential_players = players_df[(players_df['潜力值'] > 90) & (players_df['年龄'] < 25)].copy()
    print(f"潜力值超过90且年龄小于25岁的球员数量: {len(young_potential_players)}")
    
    # 筛选国际声望超过5的俱乐部
    high_reputation_clubs = teams_df[teams_df['国际声望'] > 5].copy()
    print(f"国际声望超过5的俱乐部数量: {len(high_reputation_clubs)}")
    
    # 关联两个表
    result = pd.merge(
        young_potential_players[['姓名', '俱乐部', '潜力值', '年龄']], 
        high_reputation_clubs[['俱乐部', '国际声望']], 
        on='俱乐部', 
        how='inner'
    )
    
    # 计算占比
    total_young_potential = len(young_potential_players)
    high_reputation_count = len(result)
    percentage = (high_reputation_count / total_young_potential * 100) if total_young_potential > 0 else 0
    
    print(f"所在俱乐部国际声望超过5的球员数量: {high_reputation_count}")
    print(f"占比: {percentage:.2f}%")
    
    # 显示详细结果
    result_summary = pd.DataFrame({
        '指标': ['潜力值>90且年龄<25的球员总数', '其中所在俱乐部国际声望>5的球员数', '占比(%)'],
        '数值': [total_young_potential, high_reputation_count, f'{percentage:.2f}%']
    })
    
    print(result_summary.to_markdown(index=False))

if __name__ == "__main__":
    main()
