#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题6：FIFA 21中国际声望超过5且国内声望超过5的俱乐部中，
拥有综合评分超过85球员数量最多的俱乐部是哪个？
列出俱乐部名称、高评分球员数量、国际声望和国内声望。
"""

import pandas as pd

def main():
    # 读取数据文件
    print("正在读取数据文件...")
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    # 筛选国际声望超过5且国内声望超过5的俱乐部
    high_reputation_clubs = teams_df[(teams_df['国际声望'] > 5) & (teams_df['国内声望'] > 5)].copy()
    print(f"国际声望超过5且国内声望超过5的俱乐部数量: {len(high_reputation_clubs)}")
    
    # 筛选综合评分超过85的球员
    high_rated_players = players_df[players_df['综合评分'] > 85].copy()
    print(f"综合评分超过85的球员数量: {len(high_rated_players)}")
    
    # 关联两个表
    merged_df = pd.merge(
        high_rated_players[['姓名', '俱乐部', '综合评分']], 
        high_reputation_clubs[['俱乐部', '国际声望', '国内声望']], 
        on='俱乐部', 
        how='inner'
    )
    
    # 按俱乐部统计高评分球员数量
    club_stats = merged_df.groupby(['俱乐部', '国际声望', '国内声望']).size().reset_index(name='高评分球员数量')
    
    # 找到拥有高评分球员数量最多的俱乐部
    max_count = club_stats['高评分球员数量'].max()
    top_club = club_stats[club_stats['高评分球员数量'] == max_count]
    
    print(f"最多高评分球员数量: {max_count}")
    print(top_club.to_markdown(index=False))

if __name__ == "__main__":
    main()
