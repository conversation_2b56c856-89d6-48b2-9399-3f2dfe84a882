#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题5：FIFA 21中综合评分超过85且惯用脚为左脚的球员中，
所在俱乐部联赛为"English Premier League (1)"的球员平均身价是多少？
"""

import pandas as pd

def main():
    # 读取数据文件
    print("正在读取数据文件...")
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    # 筛选综合评分超过85且惯用脚为左脚的球员
    left_foot_players = players_df[(players_df['综合评分'] > 85) & (players_df['惯用脚'] == 'Left')].copy()
    print(f"综合评分超过85且惯用脚为左脚的球员数量: {len(left_foot_players)}")
    
    # 筛选英超联赛的俱乐部
    epl_clubs = teams_df[teams_df['联赛'] == 'English Premier League (1)'].copy()
    print(f"英超俱乐部数量: {len(epl_clubs)}")
    
    # 关联两个表
    result = pd.merge(
        left_foot_players[['姓名', '俱乐部', '综合评分', '惯用脚', '身价（欧元）']], 
        epl_clubs[['俱乐部', '联赛']], 
        on='俱乐部', 
        how='inner'
    )
    
    print(f"符合条件的球员数量: {len(result)}")
    
    if len(result) > 0:
        # 计算平均身价
        avg_value = result['身价（欧元）'].mean()
        
        # 创建结果DataFrame
        summary = pd.DataFrame({
            '指标': ['符合条件的球员数量', '平均身价(百万欧元)'],
            '数值': [len(result), f'{avg_value/1000000:.2f}']
        })
        
        print(summary.to_markdown(index=False))
        
        # 显示详细球员信息
        print("\n详细球员信息:")
        detail_result = result[['姓名', '俱乐部', '综合评分', '身价（欧元）']].copy()
        detail_result['身价(百万欧元)'] = (detail_result['身价（欧元）'] / 1000000).round(2)
        detail_result = detail_result[['姓名', '俱乐部', '综合评分', '身价(百万欧元)']]
        print(detail_result.to_markdown(index=False))
    else:
        print("没有符合条件的球员")

if __name__ == "__main__":
    main()
