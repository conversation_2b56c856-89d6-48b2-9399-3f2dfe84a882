#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题7：FIFA 21中哪个联赛的俱乐部拥有的综合评分超过90球员总数最多？
列出联赛名称、高评分球员总数和该联赛俱乐部平均综合评分。
"""

import pandas as pd

def main():
    # 读取数据文件
    print("正在读取数据文件...")
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    # 筛选综合评分超过90的球员
    top_players = players_df[players_df['综合评分'] > 90].copy()
    print(f"综合评分超过90的球员数量: {len(top_players)}")
    
    # 关联球员表和球队表（注意球队表中综合评分列名前有空格）
    merged_df = pd.merge(
        top_players[['姓名', '俱乐部', '综合评分']], 
        teams_df[['俱乐部', '联赛', ' 综合评分']], 
        on='俱乐部', 
        how='inner',
        suffixes=('_球员', '_俱乐部')
    )
    
    # 按联赛统计高评分球员数量和俱乐部平均评分
    league_stats = merged_df.groupby('联赛').agg({
        '姓名': 'count',  # 高评分球员数量
        ' 综合评分': 'mean'  # 俱乐部平均评分
    }).reset_index()
    
    league_stats.columns = ['联赛名称', '高评分球员总数', '俱乐部平均综合评分']
    league_stats['俱乐部平均综合评分'] = league_stats['俱乐部平均综合评分'].round(2)
    
    # 按高评分球员总数降序排列
    league_stats = league_stats.sort_values('高评分球员总数', ascending=False)
    
    # 找到拥有高评分球员总数最多的联赛
    top_league = league_stats.iloc[0]
    
    league_name = top_league['联赛名称']
    player_count = top_league['高评分球员总数']
    avg_rating = top_league['俱乐部平均综合评分']
    
    print(f"拥有综合评分超过90球员总数最多的联赛: {league_name}")
    print(f"高评分球员总数: {player_count}")
    print(f"该联赛俱乐部平均综合评分: {avg_rating}")
    
    result = pd.DataFrame({
        '联赛名称': [league_name],
        '高评分球员总数': [player_count],
        '俱乐部平均综合评分': [avg_rating]
    })
    print(result.to_markdown(index=False))

if __name__ == "__main__":
    main()
