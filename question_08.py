#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题8：FIFA 21中综合评分超过85的球员中，所在俱乐部转会预算超过3000万欧元的球员里，
哪个国籍的球员数量最多？列出国籍、球员数量和这些球员所在俱乐部的平均转会预算。
"""

import pandas as pd

def main():
    # 读取数据文件
    print("正在读取数据文件...")
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    # 筛选综合评分超过85的球员
    high_rated_players = players_df[players_df['综合评分'] > 85].copy()
    print(f"综合评分超过85的球员数量: {len(high_rated_players)}")
    
    # 筛选转会预算超过3000万欧元的俱乐部
    rich_clubs = teams_df[teams_df['转会预算'] > 30000000].copy()
    print(f"转会预算超过3000万欧元的俱乐部数量: {len(rich_clubs)}")
    
    # 关联两个表
    merged_df = pd.merge(
        high_rated_players[['姓名', '俱乐部', '综合评分', '国籍']], 
        rich_clubs[['俱乐部', '转会预算']], 
        on='俱乐部', 
        how='inner'
    )
    
    print(f"符合条件的球员数量: {len(merged_df)}")
    
    # 按国籍统计球员数量和平均转会预算
    nationality_stats = merged_df.groupby('国籍').agg({
        '姓名': 'count',  # 球员数量
        '转会预算': 'mean'  # 平均转会预算
    }).reset_index()
    
    nationality_stats.columns = ['国籍', '球员数量', '平均转会预算']
    nationality_stats['平均转会预算(百万欧元)'] = (nationality_stats['平均转会预算'] / 1000000).round(2)
    
    # 按球员数量降序排列
    nationality_stats = nationality_stats.sort_values('球员数量', ascending=False)
    
    # 找到球员数量最多的国籍
    top_nationality = nationality_stats.iloc[0]
    
    nationality_name = top_nationality['国籍']
    player_count = top_nationality['球员数量']
    avg_budget = top_nationality['平均转会预算(百万欧元)']
    
    print(f"球员数量最多的国籍: {nationality_name}")
    print(f"球员数量: {player_count}")
    print(f"平均转会预算: {avg_budget}百万欧元")
    
    result = pd.DataFrame({
        '国籍': [nationality_name],
        '球员数量': [player_count],
        '平均转会预算(百万欧元)': [avg_budget]
    })
    print(result.to_markdown(index=False))

if __name__ == "__main__":
    main()
