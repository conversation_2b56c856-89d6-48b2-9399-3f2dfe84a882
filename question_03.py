#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题3：FIFA 21中周薪超过30万欧元且所在俱乐部国内声望超过5的球员有哪些？
列出球员姓名、俱乐部、周薪、俱乐部国内声望和国际声望。
"""

import pandas as pd

def main():
    # 读取数据文件
    print("正在读取数据文件...")
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    # 筛选周薪超过30万欧元的球员
    high_salary_players = players_df[players_df['周薪（欧元）'] > 300000].copy()
    print(f"周薪超过30万欧元的球员数量: {len(high_salary_players)}")
    
    # 筛选国内声望超过5的俱乐部
    high_domestic_reputation_clubs = teams_df[teams_df['国内声望'] > 5].copy()
    print(f"国内声望超过5的俱乐部数量: {len(high_domestic_reputation_clubs)}")
    
    # 关联两个表
    result = pd.merge(
        high_salary_players[['姓名', '俱乐部', '周薪（欧元）']], 
        high_domestic_reputation_clubs[['俱乐部', '国内声望', '国际声望']], 
        on='俱乐部', 
        how='inner'
    )
    
    # 按周薪降序排列
    result = result.sort_values('周薪（欧元）', ascending=False)
    
    # 格式化周薪显示
    result['周薪(万欧元)'] = (result['周薪（欧元）'] / 10000).round(1)
    
    # 选择最终输出的列
    final_result = result[['姓名', '俱乐部', '周薪(万欧元)', '国内声望', '国际声望']]
    
    print(f"\n符合条件的球员数量: {len(final_result)}")
    print(final_result.to_markdown(index=False))

if __name__ == "__main__":
    main()
