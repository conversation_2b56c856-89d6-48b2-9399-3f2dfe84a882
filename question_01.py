#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题1：FIFA 21中综合评分超过90且所在俱乐部转会预算超过5000万欧元的球员有哪些？
列出球员姓名、俱乐部、综合评分和俱乐部转会预算。
"""

import pandas as pd

def main():
    # 读取数据文件
    print("正在读取数据文件...")
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    # 筛选综合评分超过90的球员
    high_rated_players = players_df[players_df['综合评分'] > 90].copy()
    print(f"综合评分超过90的球员数量: {len(high_rated_players)}")
    
    # 筛选转会预算超过5000万欧元的俱乐部
    rich_clubs = teams_df[teams_df['转会预算'] > 50000000].copy()
    print(f"转会预算超过5000万欧元的俱乐部数量: {len(rich_clubs)}")
    
    # 关联两个表
    result = pd.merge(
        high_rated_players[['姓名', '俱乐部', '综合评分']], 
        rich_clubs[['俱乐部', '转会预算']], 
        on='俱乐部', 
        how='inner'
    )
    
    # 按综合评分降序排列
    result = result.sort_values('综合评分', ascending=False)
    
    # 格式化转会预算显示
    result['转会预算(百万欧元)'] = (result['转会预算'] / 1000000).round(1)
    
    # 选择最终输出的列
    final_result = result[['姓名', '俱乐部', '综合评分', '转会预算(百万欧元)']]
    
    print(f"\n符合条件的球员数量: {len(final_result)}")
    print(final_result.to_markdown(index=False))

if __name__ == "__main__":
    main()
