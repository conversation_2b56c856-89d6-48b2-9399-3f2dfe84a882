# 数据分析任务要求

## 数据集说明

本项目包含两个Excel数据文件：

1. **21年校服线上销售订单.xlsx** - 订单详细信息表

   - 店铺名称：销售店铺名称
   - 订单号：订单唯一标识符
   - 系统单号：系统内部单号
   - 仓库编码/仓库名称：发货仓库信息
   - 系统订单状态/线上订单状态/明细状态：订单各种状态信息
   - 商品编码：商品唯一标识符（用于关联小类信息表）
   - 商品名称/规格名称/品牌：商品详细信息
   - 参考进价/单价/折后单价：价格信息
   - 数量：订单商品数量
   - 应收/销售金额：金额信息
   - 发货时间/完成时间/付款时间：时间信息
   - 买家ID(客户昵称)：客户信息
2. **校服小类信息.xlsx** - 商品分类信息表

   - 款号：商品款式编号
   - SKU：商品SKU编码（与订单表关联：SKU + 规格名称中的尺码 = 商品编码）
   - 学校：校服所属学校
   - 吊牌价：商品原价
   - 销售价：商品销售价格
   - 季节：商品适用季节（春秋/夏/冬）
   - 小类：商品小类分类

## 技术要求

1. **编程语言**：使用Python进行数据分析
2. **代码组织**：每个问题对应一个独立的Python文件，文件命名格式为 `question_XX.py`（XX为问题编号，如01、02等）
3. **数据处理**：使用pandas库进行Excel文件读取和处理
4. **输出格式**：所有结果必须以Markdown格式的DataFrame输出，使用 `df.to_markdown()` 方法
5. **数据关联**：通过SKU与商品编码进行关联分析（商品编码 = SKU + 规格名称中的尺码数字）
6. **折扣率计算**：折扣率 = (折后单价 / 吊牌价) × 100%

## 分析问题

**注意：所有分析均基于2021年的数据**

1. 2021年销售额最高的校服小类是什么？具体销售额是多少？
2. 列出2021年所有发货量超过1000件的校服小类，包含小类名称和发货量
3. 2021年各季节校服的销售额占比分别是多少？
4. 2021年所有校服中，哪个学校的校服销售额最高？具体数值是多少？
5. 2021年所有校服中，哪个季节的校服平均折扣率（折后单价/吊牌价）最低？具体数值是多少？
6. 列出2021年所有发货量超过500件的学校校服，包含学校名称和发货量
7. 2021年所有校服中，哪个学校的校服退货率（基于订单状态统计）最高？具体数值是多少？
8. 2021年所有校服中，哪个季节的校服销售额占比最高？具体数值是多少？

## 输出要求

1. 每个Python文件应包含完整的数据处理流程
2. 结果必须使用 `print(df.to_markdown(index=False))` 输出为Markdown格式
3. 对于统计类问题（如"哪个类别最多"），输出应包含排序后的完整结果
4. 对于列表类问题，输出应包含所有符合条件的记录
5. 代码应包含适当的注释说明处理逻辑
