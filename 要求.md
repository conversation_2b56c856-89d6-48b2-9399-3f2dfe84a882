# FIFA 21 数据分析任务要求

## 数据集说明

本项目包含两个CSV数据文件：

1. **players_fifa21.csv** - 球员详细信息表（共90个字段）

   主要字段包括：
   - **基本信息**：ID、姓名、全名、年龄、身高、体重、国籍
   - **评分数据**：综合评分、潜力值、成长空间、总属性值、基础属性值
   - **位置信息**：可打位置、最佳位置、俱乐部位置
   - **俱乐部信息**：俱乐部、身价（欧元）、周薪（欧元）、解约金、合同到期时间
   - **国家队信息**：国家队、国家队位置、国家队号码
   - **技术属性**：惯用脚、国际知名度、技巧动作等级、进攻积极性、防守积极性
   - **能力评分**：速度总评、射门总评、传球总评、盘带总评、防守总评、身体素质总评
   - **详细技能**：传中、终结能力、头球精度、短传、凌空抽射、盘带等40+项技能
   - **位置评分**：前锋评分、边锋评分、中场评分、后卫评分、门将评分等17个位置评分

2. **teams_fifa21.csv** - 俱乐部信息表（共14个字段）

   - **基本信息**：编号、俱乐部、联赛、联赛ID
   - **评分数据**：综合评分、进攻评分、中场评分、防守评分
   - **财务信息**：转会预算
   - **声望信息**：国内声望、国际声望
   - **队伍信息**：球员数量、首发平均年龄、全队平均年龄

## 技术要求

1. **编程语言**：使用Python进行数据分析
2. **代码组织**：每个问题对应一个独立的Python文件，文件命名格式为 `question_XX.py`（XX为问题编号，如01、02等）
3. **数据处理**：使用pandas库进行CSV文件读取和处理
4. **输出格式**：所有结果必须以Markdown格式的DataFrame输出，使用 `df.to_markdown()` 方法
5. **数据关联**：通过俱乐部字段进行球员表和俱乐部表的关联分析
6. **数据清洗**：注意处理缺失值和异常数据

## 分析问题

**注意：所有分析均基于FIFA 21的数据**

1. FIFA 21中综合评分超过90的球员有哪些？列出球员姓名、俱乐部和综合评分。
2. FIFA 21中潜力值超过90且年龄小于25岁的球员占比是多少？
3. FIFA 21中周薪超过30万欧元的球员有哪些？列出球员姓名、俱乐部和周薪。
4. FIFA 21中哪个俱乐部的转会预算最高？具体数值是多少？
5. FIFA 21中综合评分超过85且惯用脚为左脚的球员平均身价是多少？
6. FIFA 21中国际声望超过5且国内声望超过5的俱乐部有哪些？列出俱乐部名称、国际声望和国内声望。
7. FIFA 21中哪个联赛的俱乐部平均综合评分最高？具体数值是多少？
8. FIFA 21中综合评分超过85的球员中，哪个国籍的球员数量最多？具体数量是多少？

## 输出要求

1. 每个Python文件应包含完整的数据处理流程
2. 结果必须使用 `print(df.to_markdown(index=False))` 输出为Markdown格式
3. 对于统计类问题（如"哪个类别最多"），输出应包含排序后的完整结果
4. 对于列表类问题，输出应包含所有符合条件的记录
5. 代码应包含适当的注释说明处理逻辑
6. 处理数据时注意空值和异常值的处理
