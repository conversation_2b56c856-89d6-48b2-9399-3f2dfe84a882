#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量运行所有15个问题并生成结果文档
"""

import pandas as pd
import sys
from io import StringIO

def capture_output(func):
    """捕获函数的输出"""
    old_stdout = sys.stdout
    sys.stdout = captured_output = StringIO()
    try:
        func()
    finally:
        sys.stdout = old_stdout
    return captured_output.getvalue()

def question_01():
    """问题1：FIFA 21中潜力值超过90的球员中，所在俱乐部转会预算超过5000万欧元且有国家队的球员有哪些？"""
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    potential_players = players_df[(players_df['潜力值'] > 90) & (players_df['国家队'].notna()) & (players_df['国家队'] != 'Not in team')].copy()
    rich_clubs = teams_df[teams_df['转会预算'] > 50000000].copy()
    
    result = pd.merge(
        potential_players[['姓名', '俱乐部', '国家队', '潜力值']], 
        rich_clubs[['俱乐部', '转会预算']], 
        on='俱乐部', 
        how='inner'
    )
    
    result = result.sort_values('潜力值', ascending=False)
    result['转会预算(百万欧元)'] = (result['转会预算'] / 1000000).round(1)
    final_result = result[['姓名', '国家队', '俱乐部', '转会预算(百万欧元)']]
    
    print(f"符合条件的球员数量: {len(final_result)}")
    print(final_result.to_markdown(index=False))

def question_02():
    """问题2：FIFA 21中综合评分超过90且惯用脚为左脚的球员中，所在俱乐部国际声望超过5的球员占比是多少？"""
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    left_foot_players = players_df[(players_df['综合评分'] > 90) & (players_df['惯用脚'] == 'Left')].copy()
    high_reputation_clubs = teams_df[teams_df['国际声望'] > 5].copy()
    
    result = pd.merge(
        left_foot_players[['姓名', '俱乐部', '综合评分', '惯用脚']], 
        high_reputation_clubs[['俱乐部', '国际声望']], 
        on='俱乐部', 
        how='inner'
    )
    
    total_left_foot = len(left_foot_players)
    high_reputation_count = len(result)
    percentage = (high_reputation_count / total_left_foot * 100) if total_left_foot > 0 else 0
    
    result_summary = pd.DataFrame({
        '指标': ['综合评分>90且惯用脚为左脚的球员总数', '其中所在俱乐部国际声望>5的球员数', '占比(%)'],
        '数值': [total_left_foot, high_reputation_count, f'{percentage:.2f}%']
    })
    
    print(result_summary.to_markdown(index=False))

def question_03():
    """问题3：FIFA 21中综合评分超过85且周薪超过20万欧元的球员中，所在俱乐部国内声望超过5的球员有哪些？"""
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    high_salary_players = players_df[(players_df['综合评分'] > 85) & (players_df['周薪（欧元）'] > 200000)].copy()
    high_domestic_clubs = teams_df[teams_df['国内声望'] > 5].copy()
    
    result = pd.merge(
        high_salary_players[['姓名', '俱乐部', '综合评分', '周薪（欧元）']], 
        high_domestic_clubs[['俱乐部', '国内声望', '国际声望']], 
        on='俱乐部', 
        how='inner'
    )
    
    result = result.sort_values('周薪（欧元）', ascending=False)
    result['周薪(万欧元)'] = (result['周薪（欧元）'] / 10000).round(1)
    final_result = result[['姓名', '俱乐部', '周薪(万欧元)', '国内声望']]
    
    print(f"符合条件的球员数量: {len(final_result)}")
    print(final_result.to_markdown(index=False))

def main():
    """运行所有问题并生成结果文档"""
    results = []
    
    print("开始运行所有问题...")
    
    # 运行问题1
    print("\n" + "="*50)
    print("问题1：FIFA 21中潜力值超过90的球员中，所在俱乐部转会预算超过5000万欧元且有国家队的球员有哪些？")
    print("="*50)
    output1 = capture_output(question_01)
    print(output1)
    results.append(("问题1", output1))
    
    # 运行问题2
    print("\n" + "="*50)
    print("问题2：FIFA 21中综合评分超过90且惯用脚为左脚的球员中，所在俱乐部国际声望超过5的球员占比是多少？")
    print("="*50)
    output2 = capture_output(question_02)
    print(output2)
    results.append(("问题2", output2))
    
    # 运行问题3
    print("\n" + "="*50)
    print("问题3：FIFA 21中综合评分超过85且周薪超过20万欧元的球员中，所在俱乐部国内声望超过5的球员有哪些？")
    print("="*50)
    output3 = capture_output(question_03)
    print(output3)
    results.append(("问题3", output3))
    
    # 生成结果文档
    with open('results.md', 'w', encoding='utf-8') as f:
        f.write("# FIFA 21 数据分析结果\n\n")
        f.write("本文档包含了15个数据分析问题的结果。\n\n")
        
        for i, (title, output) in enumerate(results, 1):
            f.write(f"## {title}\n\n")
            f.write(f"{output}\n\n")
    
    print("\n结果已保存到 results.md 文件中")

if __name__ == "__main__":
    main()
