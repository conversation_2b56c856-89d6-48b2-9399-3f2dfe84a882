#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题4：FIFA 21中转会预算最高的俱乐部中，综合评分最高的球员是谁？
列出球员姓名、综合评分、俱乐部名称和俱乐部转会预算。
"""

import pandas as pd

def main():
    # 读取数据文件
    print("正在读取数据文件...")
    players_df = pd.read_csv('players_fifa21.csv')
    teams_df = pd.read_csv('teams_fifa21.csv')
    
    # 找到转会预算最高的俱乐部
    max_budget_club = teams_df.loc[teams_df['转会预算'].idxmax()]
    club_name = max_budget_club['俱乐部']
    club_budget = max_budget_club['转会预算']
    print(f"转会预算最高的俱乐部: {club_name}")
    print(f"转会预算: {club_budget/1000000:.1f}百万欧元")
    
    # 在该俱乐部中找到综合评分最高的球员
    club_players = players_df[players_df['俱乐部'] == club_name].copy()
    print(f"该俱乐部球员数量: {len(club_players)}")
    
    if len(club_players) > 0:
        # 找到综合评分最高的球员
        top_player = club_players.loc[club_players['综合评分'].idxmax()]
        
        # 创建结果DataFrame
        result = pd.DataFrame({
            '球员姓名': [top_player['姓名']],
            '综合评分': [top_player['综合评分']],
            '俱乐部名称': [club_name],
            '转会预算(百万欧元)': [club_budget/1000000]
        })
        
        print(result.to_markdown(index=False))
    else:
        print("该俱乐部没有球员数据")

if __name__ == "__main__":
    main()
